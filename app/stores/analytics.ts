import { defineStore } from 'pinia'
import type {
    AnalyticsProduct,
    UtmParameters,
    GdprSettings,
    BookingFormData,
    ItineraryClass,
    ItineraryType,
} from '~/types/analytics'

export const useAnalyticsStore = defineStore('analytics', () => {
    // GDPR state
    const gdpr = ref<GdprSettings>({
        isAccepted: false,
        isEuropeanUser: false,
        showBanner: false,
    })

    // UTM tracking state
    const utm = ref<UtmParameters>({})
    const utmTimestamp = ref<number | null>(null)

    // Current user session
    const session = ref({
        gaClientId: null as string | null,
        deviceId: null as string | null,
        sessionStart: Date.now(),
        pageViews: 0,
    })

    // Current product/offer data
    const currentProduct = ref<AnalyticsProduct | null>(null)

    // Booking form data
    const bookingData = ref<Partial<BookingFormData>>({})

    // Analytics readiness
    const isInitialized = ref(false)
    const isGtmLoaded = ref(false)

    // Getters
    /**
     * Check if analytics can be used
     */
    const canUseAnalytics = computed(() => {
        return gdpr.value.isAccepted && isGtmLoaded.value
    })

    /**
     * Check if UTM data is fresh (less than 24 hours)
     */
    const isUtmDataFresh = computed(() => {
        if (!utmTimestamp.value) {
            return false
        }

        const age = Date.now() - utmTimestamp.value

        return age < 24 * 60 * 60 * 1000 // 24 hours
    })

    /**
     * Get current session duration
     */
    const sessionDuration = computed(() => {
        return Date.now() - session.value.sessionStart
    })

    /**
     * Check if user has UTM parameters
     */
    const hasUtmData = computed(() => {
        return Object.keys(utm.value).length > 0
    })

    // Actions
    /**
     * Initialize analytics store
     */
    const initialize = async () => {
        if (isInitialized.value) {
            return
        }

        try {
            // Initialize composables
            const { gdprSettings } = useGdprCompliance()
            const { utmData } = useUtmTracking()
            const { getGaClientId, getDeviceId, isGtmLoaded: gtmLoaded } = useGoogleAnalytics()

            // Watch for changes
            watch(gdprSettings, (newSettings) => {
                gdpr.value = { ...newSettings }
            }, { deep: true, immediate: true })

            watch(utmData, (newUtm) => {
                utm.value = { ...newUtm }
                utmTimestamp.value = Date.now()
            }, { deep: true, immediate: true })

            watch(gtmLoaded, (loaded) => {
                isGtmLoaded.value = loaded
            }, { immediate: true })

            // Update session data
            if (import.meta.client) {
                session.value.gaClientId = getGaClientId()
                session.value.deviceId = getDeviceId()
            }

            isInitialized.value = true
        } catch (error) {
            console.error('[Analytics Store] Initialization error:', error)
        }
    }

    /**
     * Update GDPR settings
     */
    const updateGdpr = (settings: Partial<GdprSettings>) => {
        gdpr.value = { ...gdpr.value, ...settings }
    }

    /**
     * Accept GDPR consent
     */
    const acceptGdpr = () => {
        const { acceptConsent } = useGdprCompliance()
        acceptConsent()
    }

    /**
     * Reject GDPR consent
     */
    const rejectGdpr = () => {
        const { rejectConsent } = useGdprCompliance()
        rejectConsent()
    }

    /**
     * Update UTM data
     */
    const updateUtm = (utmData: UtmParameters) => {
        utm.value = { ...utmData }
        utmTimestamp.value = Date.now()
    }

    /**
     * Clear UTM data
     */
    const clearUtm = () => {
        utm.value = {}
        utmTimestamp.value = null
    }

    /**
     * Set current product for tracking
     */
    const setCurrentProduct = (product: AnalyticsProduct) => {
        currentProduct.value = product
    }

    /**
     * Clear current product
     */
    const clearCurrentProduct = () => {
        currentProduct.value = null
    }

    /**
     * Update booking form data
     */
    const updateBookingData = (data: Partial<BookingFormData>) => {
        bookingData.value = { ...bookingData.value, ...data }
    }

    /**
     * Clear booking data
     */
    const clearBookingData = () => {
        bookingData.value = {}
    }

    /**
     * Track page view
     */
    const trackPageView = () => {
        session.value.pageViews++
    }

    /**
     * Track product detail view
     */
    const trackProductDetail = async (
        routeName: string,
        routeId: string,
        price: number,
        tripType: ItineraryType,
        serviceClass: ItineraryClass,
    ) => {
        if (!canUseAnalytics.value) {
            return
        }

        const { trackProductDetail: trackDetail } = useEcommerce()
        const { sendDimensions, createProduct } = useGoogleAnalytics()

        // Create and store product
        const product = createProduct(routeName, routeId, price, tripType, serviceClass)
        setCurrentProduct(product)

        // Track the event
        await trackDetail(routeName, routeId, price, tripType, serviceClass)

        // Send custom dimensions
        sendDimensions(product)
    }

    /**
     * Track add to cart
     */
    const trackAddToCart = async (
        routeName: string,
        routeId: string,
        price: number,
        tripType: ItineraryType,
        serviceClass: ItineraryClass,
        quantity: number = 1,
    ) => {
        if (!canUseAnalytics.value) {
            return
        }

        const { trackAddToCart: trackAdd } = useEcommerce()
        await trackAdd(routeName, routeId, price, tripType, serviceClass, quantity)
    }

    /**
     * Track product click
     */
    const trackProductClick = async (
        routeName: string,
        routeId: string,
        price: number,
        tripType: ItineraryType,
        serviceClass: ItineraryClass,
    ) => {
        if (!canUseAnalytics.value) {
            return
        }

        const { trackProductClick: trackClick } = useEcommerce()
        await trackClick(routeName, routeId, price, tripType, serviceClass)
    }

    /**
     * Reset store state
     */
    const reset = () => {
        gdpr.value = {
            isAccepted: false,
            isEuropeanUser: false,
            showBanner: false,
        }
        utm.value = {}
        utmTimestamp.value = null
        session.value = {
            gaClientId: null,
            deviceId: null,
            sessionStart: Date.now(),
            pageViews: 0,
        }
        currentProduct.value = null
        bookingData.value = {}
        isInitialized.value = false
        isGtmLoaded.value = false
    }

    return {
        // State
        gdpr: readonly(gdpr),
        utm: readonly(utm),
        utmTimestamp: readonly(utmTimestamp),
        session: readonly(session),
        currentProduct: readonly(currentProduct),
        bookingData: readonly(bookingData),
        isInitialized: readonly(isInitialized),
        isGtmLoaded: readonly(isGtmLoaded),

        // Getters
        canUseAnalytics,
        isUtmDataFresh,
        sessionDuration,
        hasUtmData,

        // Actions
        initialize,
        updateGdpr,
        acceptGdpr,
        rejectGdpr,
        updateUtm,
        clearUtm,
        setCurrentProduct,
        clearCurrentProduct,
        updateBookingData,
        clearBookingData,
        trackPageView,
        trackProductDetail,
        trackAddToCart,
        trackProductClick,
        reset,
    }
})
